using ScoreboardCsvIngestor.Services;

namespace ScoreboardCsvIngestor.Models;

public class ApiResponse<T>
{
    public bool Success { get; set; }
    public string CorrelationId { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public T? Data { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<ValidationError> ValidationErrors { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class CsvProcessingResponse
{
    public int TotalRowsProcessed { get; set; }
    public int ValidRowsCount { get; set; }
    public int InvalidRowsCount { get; set; }
    public int RecordsInserted { get; set; }
    public double ProcessingTimeMs { get; set; }
}

public class HealthCheckResponse
{
    public string Status { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, string> Checks { get; set; } = new();
}
