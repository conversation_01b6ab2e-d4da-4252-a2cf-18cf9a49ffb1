using ScoreboardCsvIngestor.Models;

namespace ScoreboardCsvIngestor.Services;

public interface IScoreboardDataService
{
    Task<DataInsertResult> InsertRecordsAsync(List<CsvScoreboardRecord> records);
    Task<bool> TestConnectionAsync();
}

public class DataInsertResult
{
    public bool IsSuccess { get; set; }
    public int RecordsInserted { get; set; }
    public int RecordsFailed { get; set; }
    public List<string> Errors { get; set; } = new();
    public TimeSpan ProcessingTime { get; set; }
}
