namespace ScoreboardCsvIngestor.Exceptions;

public class CsvProcessingException : Exception
{
    public List<string> ValidationErrors { get; }
    public string CorrelationId { get; }

    public CsvProcessingException(string message, string correlationId) : base(message)
    {
        CorrelationId = correlationId;
        ValidationErrors = new List<string>();
    }

    public CsvProcessingException(string message, string correlationId, List<string> validationErrors) : base(message)
    {
        CorrelationId = correlationId;
        ValidationErrors = validationErrors ?? new List<string>();
    }

    public CsvProcessingException(string message, string correlationId, Exception innerException) : base(message, innerException)
    {
        CorrelationId = correlationId;
        ValidationErrors = new List<string>();
    }
}

public class DatabaseConnectionException : Exception
{
    public string CorrelationId { get; }

    public DatabaseConnectionException(string message, string correlationId) : base(message)
    {
        CorrelationId = correlationId;
    }

    public DatabaseConnectionException(string message, string correlationId, Exception innerException) : base(message, innerException)
    {
        CorrelationId = correlationId;
    }
}
