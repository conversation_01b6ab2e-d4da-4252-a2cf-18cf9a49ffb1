using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using CsvHelper.TypeConversion;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace ScoreboardCsvIngestor.Models;

/// <summary>
/// Represents a single row from the CSV file with exact column mapping
/// </summary>
public class CsvScoreboardRecord
{
    [Index(0)]
    [Name("Store Name")]
    [Required(ErrorMessage = "Store Name is required")]
    [MaxLength(255, ErrorMessage = "Store Name cannot exceed 255 characters")]
    public string StoreName { get; set; } = string.Empty;

    [Index(1)]
    [Name("Lane Departure Time")]
    [Required(ErrorMessage = "Lane Departure Time is required")]
    public DateTime LaneDepartureTime { get; set; }

    [Index(2)]
    [Name("Car Type")]
    [Required(ErrorMessage = "Car Type is required")]
    [MaxLength(100, ErrorMessage = "Car Type cannot exceed 100 characters")]
    public string CarType { get; set; } = string.Empty;

    [Index(3)]
    [Name("Lane Number")]
    [Required(ErrorMessage = "Lane Number is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Lane Number must be a positive integer")]
    public int LaneNumber { get; set; }

    [Index(4)]
    [Name("Order Time")]
    [Required(ErrorMessage = "Order Time is required")]
    [Range(0, int.MaxValue, ErrorMessage = "Order Time must be a non-negative integer")]
    public int OrderTime { get; set; }

    [Index(5)]
    [Name("Pickup Time")]
    [Required(ErrorMessage = "Pickup Time is required")]
    [Range(0, int.MaxValue, ErrorMessage = "Pickup Time must be a non-negative integer")]
    public int PickupTime { get; set; }

    [Index(6)]
    [Name("Queue Time")]
    [Required(ErrorMessage = "Queue Time is required")]
    [Range(0, int.MaxValue, ErrorMessage = "Queue Time must be a non-negative integer")]
    public int QueueTime { get; set; }

    [Index(7)]
    [Name("Total Time")]
    [Required(ErrorMessage = "Total Time is required")]
    [Range(0, int.MaxValue, ErrorMessage = "Total Time must be a non-negative integer")]
    public int TotalTime { get; set; }

    /// <summary>
    /// Converts CSV record to database entity
    /// </summary>
    public ScoreboardRecord ToScoreboardRecord()
    {
        return new ScoreboardRecord
        {
            StoreName = StoreName.Trim(),
            LaneDepartureTime = LaneDepartureTime,
            CarType = CarType.Trim(),
            LaneNumber = LaneNumber,
            OrderTime = OrderTime,
            PickupTime = PickupTime,
            QueueTime = QueueTime,
            TotalTime = TotalTime
        };
    }
}

/// <summary>
/// Custom DateTime converter for the specific date format in the CSV
/// </summary>
public class CustomDateTimeConverter : DefaultTypeConverter
{
    public override object? ConvertFromString(string? text, IReaderRow row, MemberMapData memberMapData)
    {
        if (string.IsNullOrWhiteSpace(text))
            return null;

        // Try multiple date formats
        string[] formats = { "dd/MM/yyyy h:mm tt", "dd/MM/yyyy H:mm", "M/d/yyyy h:mm tt", "M/d/yyyy H:mm" };

        foreach (var format in formats)
        {
            if (DateTime.TryParseExact(text, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
            {
                return result;
            }
        }

        // Fallback to default parsing
        if (DateTime.TryParse(text, out DateTime fallbackResult))
        {
            return fallbackResult;
        }

        throw new TypeConverterException(this, memberMapData, text, row.Context, $"Unable to parse '{text}' as DateTime");
    }
}

/// <summary>
/// Class map for CsvScoreboardRecord to configure custom type converters
/// </summary>
public class CsvScoreboardRecordMap : ClassMap<CsvScoreboardRecord>
{
    public CsvScoreboardRecordMap()
    {
        Map(m => m.StoreName).Index(0).Name("Store Name");
        Map(m => m.LaneDepartureTime).Index(1).Name("Lane Departure Time").TypeConverter<CustomDateTimeConverter>();
        Map(m => m.CarType).Index(2).Name("Car Type");
        Map(m => m.LaneNumber).Index(3).Name("Lane Number");
        Map(m => m.OrderTime).Index(4).Name("Order Time");
        Map(m => m.PickupTime).Index(5).Name("Pickup Time");
        Map(m => m.QueueTime).Index(6).Name("Queue Time");
        Map(m => m.TotalTime).Index(7).Name("Total Time");
    }
}
