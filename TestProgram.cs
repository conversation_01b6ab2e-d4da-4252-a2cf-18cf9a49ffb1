using Microsoft.EntityFrameworkCore;
using ScoreboardCsvIngestor.Configuration;
using ScoreboardCsvIngestor.Data;
using ScoreboardCsvIngestor.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Get app settings
var appSettings = builder.Configuration.GetAppSettings();
builder.Services.AddSingleton(appSettings);

// Add Entity Framework
builder.Services.AddDbContext<ScoreboardDbContext>(options =>
{
    options.UseSqlServer(appSettings.SqlConnectionString);
    if (appSettings.EnableDetailedLogging)
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});

// Add custom services
builder.Services.AddScoped<ICsvParserService, CsvParserService>();
builder.Services.AddScoped<IScoreboardDataService, ScoreboardDataService>();

// Configure logging
builder.Services.AddLogging(builder =>
{
    builder.AddConsole();
    if (appSettings.EnableDetailedLogging)
    {
        builder.SetMinimumLevel(LogLevel.Debug);
    }
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

app.Run();
