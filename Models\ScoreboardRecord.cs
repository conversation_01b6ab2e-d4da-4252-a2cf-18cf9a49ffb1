using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ScoreboardCsvIngestor.Models;

[Table("ScoreboardRecords")]
public class ScoreboardRecord
{
    [Key]
    public int Id { get; set; }

    [Required]
    [MaxLength(255)]
    [Column("StoreName")]
    public string StoreName { get; set; } = string.Empty;

    [Required]
    [Column("LaneDepartureTime")]
    public DateTime LaneDepartureTime { get; set; }

    [Required]
    [MaxLength(100)]
    [Column("CarType")]
    public string CarType { get; set; } = string.Empty;

    [Required]
    [Column("LaneNumber")]
    public int LaneNumber { get; set; }

    [Required]
    [Column("OrderTime")]
    public int OrderTime { get; set; }

    [Required]
    [Column("PickupTime")]
    public int PickupTime { get; set; }

    [Required]
    [Column("QueueTime")]
    public int QueueTime { get; set; }

    [Required]
    [Column("TotalTime")]
    public int TotalTime { get; set; }

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    [Column("ProcessedAt")]
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
}
