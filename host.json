{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}, "enableLiveMetricsFilters": true}, "logLevel": {"default": "Information", "Microsoft.AspNetCore": "Warning"}}, "functionTimeout": "00:05:00", "http": {"routePrefix": "api", "maxOutstandingRequests": 200, "maxConcurrentRequests": 100, "dynamicThrottlesEnabled": true}, "extensions": {"http": {"maxRequestBodySize": 52428800}}}