-- Create the ScoreboardDb database
CREATE DATABASE ScoreboardDb;
GO

USE ScoreboardDb;
GO

-- Create the ScoreboardRecords table
CREATE TABLE ScoreboardRecords (
    Id int IDENTITY(1,1) PRIMARY KEY,
    StoreName nvarchar(255) NOT NULL,
    LaneDepartureTime datetime2 NOT NULL,
    CarType nvarchar(100) NOT NULL,
    Lane<PERSON><PERSON>ber int NOT NULL,
    OrderTime int NOT NULL,
    PickupTime int NOT NULL,
    QueueTime int NOT NULL,
    TotalTime int NOT NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    ProcessedAt datetime2 NOT NULL DEFAULT GETUTCDATE()
);
GO

-- Create indexes for common queries
CREATE INDEX IX_ScoreboardRecords_StoreName ON ScoreboardRecords(StoreName);
CREATE INDEX IX_ScoreboardRecords_LaneDepartureTime ON ScoreboardRecords(LaneDepartureTime);
CREATE INDEX IX_ScoreboardRecords_CreatedAt ON ScoreboardRecords(CreatedAt);
GO
