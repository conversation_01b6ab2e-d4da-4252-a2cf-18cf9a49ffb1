

using Microsoft.Extensions.Configuration;

namespace ScoreboardCsvIngestor.Configuration;

public class AppSettings
{
    public string SqlConnectionString { get; set; } = string.Empty;
    public int MaxCsvRows { get; set; } = 500;
    public bool EnableDetailedLogging { get; set; } = false;
    public string ApplicationInsightsConnectionString { get; set; } = string.Empty;
}

public static class ConfigurationExtensions
{
    public static AppSettings GetAppSettings(this IConfiguration configuration)
    {
        return new AppSettings
        {
            SqlConnectionString = configuration.GetConnectionString("SqlConnectionString") ?? string.Empty,
            MaxCsvRows = configuration.GetValue<int>("MaxCsvRows", 500),
            EnableDetailedLogging = configuration.GetValue<bool>("EnableDetailedLogging", false),
            ApplicationInsightsConnectionString = configuration.GetValue<string>("APPLICATIONINSIGHTS_CONNECTION_STRING") ?? string.Empty
        };
    }
}
