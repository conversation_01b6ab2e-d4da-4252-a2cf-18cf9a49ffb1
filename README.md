# Scoreboard CSV Ingestor - Azure Function App

This Azure Function App processes CSV files containing scoreboard data and stores them in Azure SQL Database. It's designed to be called by Azure Logic Apps for automated CSV processing.

## Features

- **HTTP-triggered Azure Function** for CSV file processing
- **Robust CSV parsing** with validation using CsvHelper
- **Azure SQL Database integration** with Entity Framework Core
- **Comprehensive error handling** and validation
- **Structured logging** with Application Insights support
- **Health check endpoint** for monitoring
- **Configurable row limits** (default: 500 rows)

## CSV Format

The function expects CSV files with the following exact column structure:

1. **Store Name** (string) - Name of the store
2. **Lane Departure Time** (datetime) - When the vehicle departed the lane
3. **Car Type** (string) - Type of vehicle
4. **Lane Number** (integer) - Lane identifier (must be positive)
5. **Order Time** (integer) - Time for order processing (non-negative)
6. **Pickup Time** (integer) - Time for pickup (non-negative)
7. **Queue Time** (integer) - Time spent in queue (non-negative)
8. **Total Time** (integer) - Total processing time (non-negative)

## API Endpoints

### POST /api/csv/upload
Processes CSV file uploads from Logic Apps.

**Request:**
- Content-Type: text/csv or multipart/form-data
- Body: CSV file content

**Response:**
```json
{
  "success": true,
  "correlationId": "guid",
  "message": "CSV processed successfully",
  "data": {
    "totalRowsProcessed": 100,
    "validRowsCount": 98,
    "invalidRowsCount": 2,
    "recordsInserted": 98,
    "processingTimeMs": 1250.5
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### GET /api/health
Health check endpoint for monitoring.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "checks": {
    "database": "ok"
  }
}
```

## Configuration

### Local Development (local.settings.json)
```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "APPLICATIONINSIGHTS_CONNECTION_STRING": "",
    "SqlConnectionString": "Server=(localdb)\\mssqllocaldb;Database=ScoreboardDb;Trusted_Connection=true;MultipleActiveResultSets=true;",
    "MaxCsvRows": "500",
    "EnableDetailedLogging": "true"
  }
}
```

### Azure Deployment
Set the following application settings in your Azure Function App:

- `SqlConnectionString`: Your Azure SQL Database connection string
- `APPLICATIONINSIGHTS_CONNECTION_STRING`: Application Insights connection string
- `MaxCsvRows`: Maximum number of rows to process (default: 500)
- `EnableDetailedLogging`: Enable detailed logging for debugging (true/false)

## Database Schema

The application creates a `ScoreboardRecords` table with the following structure:

```sql
CREATE TABLE ScoreboardRecords (
    Id int IDENTITY(1,1) PRIMARY KEY,
    StoreName nvarchar(255) NOT NULL,
    LaneDepartureTime datetime2 NOT NULL,
    CarType nvarchar(100) NOT NULL,
    LaneNumber int NOT NULL,
    OrderTime int NOT NULL,
    PickupTime int NOT NULL,
    QueueTime int NOT NULL,
    TotalTime int NOT NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    ProcessedAt datetime2 NOT NULL DEFAULT GETUTCDATE()
);
```

## Development Setup

1. **Prerequisites:**
   - .NET 8.0 SDK
   - Azure Functions Core Tools v4
   - SQL Server LocalDB (for local development)

2. **Clone and setup:**
   ```bash
   git clone <repository-url>
   cd scoreboard-csv-ingestor
   dotnet restore
   ```

3. **Database setup:**
   ```bash
   dotnet ef database update
   ```

4. **Run locally:**
   ```bash
   func start
   ```

## Deployment

1. **Create Azure resources:**
   - Azure Function App (.NET 8, Consumption plan)
   - Azure SQL Database
   - Application Insights (optional)

2. **Deploy using Azure CLI:**
   ```bash
   func azure functionapp publish <function-app-name>
   ```

3. **Configure application settings** in the Azure portal

## Error Handling

The function provides comprehensive error handling for:

- **Invalid CSV format**: Returns 400 Bad Request with detailed validation errors
- **Missing or malformed data**: Validates each field and reports specific issues
- **Database connection issues**: Returns 503 Service Unavailable
- **Row limit exceeded**: Stops processing and reports the limit breach
- **Unexpected errors**: Returns 500 Internal Server Error with correlation ID

## Logging

All operations are logged with structured logging including:

- Correlation IDs for request tracking
- Performance metrics
- Validation errors
- Database operation results
- Error details with stack traces

## Monitoring

- Use Application Insights for monitoring and alerting
- Health check endpoint for uptime monitoring
- Structured logs for debugging and analysis
- Performance counters for processing times

## Security

- Function-level authorization (no authentication required as specified)
- Input validation and sanitization
- SQL injection protection via Entity Framework
- Error message sanitization to prevent information disclosure
