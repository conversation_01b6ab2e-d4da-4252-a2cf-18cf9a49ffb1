using Microsoft.AspNetCore.Mvc;
using ScoreboardCsvIngestor.Models;
using ScoreboardCsvIngestor.Services;

namespace TestApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CsvController : ControllerBase
{
    private readonly ILogger<CsvController> _logger;
    private readonly ICsvParserService _csvParserService;
    private readonly IScoreboardDataService _dataService;

    public CsvController(
        ILogger<CsvController> logger,
        ICsvParserService csvParserService,
        IScoreboardDataService dataService)
    {
        _logger = logger;
        _csvParserService = csvParserService;
        _dataService = dataService;
    }

    [HttpPost("upload")]
    public async Task<IActionResult> ProcessCsvUpload()
    {
        var correlationId = Guid.NewGuid().ToString();
        _logger.LogInformation("CSV processing request started. CorrelationId: {CorrelationId}", correlationId);

        try
        {
            // Read request body
            using var reader = new StreamReader(Request.Body);
            var csvContent = await reader.ReadToEndAsync();

            if (string.IsNullOrWhiteSpace(csvContent))
            {
                _logger.LogWarning("Request body is empty. CorrelationId: {CorrelationId}", correlationId);
                return BadRequest(new ApiResponse<object>
                {
                    Success = false,
                    CorrelationId = correlationId,
                    Message = "Request body is required",
                    Errors = new List<string> { "CSV content cannot be empty" }
                });
            }

            // Test database connection first
            if (!await _dataService.TestConnectionAsync())
            {
                _logger.LogError("Database connection test failed. CorrelationId: {CorrelationId}", correlationId);
                return StatusCode(503, new ApiResponse<object>
                {
                    Success = false,
                    CorrelationId = correlationId,
                    Message = "Database connection failed",
                    Errors = new List<string> { "Unable to connect to database" }
                });
            }

            // Parse CSV content
            _logger.LogInformation("Starting CSV parsing. CorrelationId: {CorrelationId}", correlationId);
            var parseResult = await _csvParserService.ParseCsvAsync(csvContent);

            if (!parseResult.IsSuccess)
            {
                _logger.LogWarning("CSV parsing failed. Errors: {Errors}. CorrelationId: {CorrelationId}", 
                    string.Join(", ", parseResult.Errors), correlationId);
                
                return BadRequest(new ApiResponse<object>
                {
                    Success = false,
                    CorrelationId = correlationId,
                    Message = "CSV parsing failed",
                    Errors = parseResult.Errors,
                    ValidationErrors = parseResult.ValidationErrors
                });
            }

            if (parseResult.Records.Count == 0)
            {
                _logger.LogWarning("No valid records found in CSV. CorrelationId: {CorrelationId}", correlationId);
                return BadRequest(new ApiResponse<object>
                {
                    Success = false,
                    CorrelationId = correlationId,
                    Message = "No valid records found in CSV"
                });
            }

            // Insert records into database
            _logger.LogInformation("Starting database insertion for {RecordCount} records. CorrelationId: {CorrelationId}", 
                parseResult.Records.Count, correlationId);
            
            var insertResult = await _dataService.InsertRecordsAsync(parseResult.Records);

            if (!insertResult.IsSuccess)
            {
                _logger.LogError("Database insertion failed. Errors: {Errors}. CorrelationId: {CorrelationId}", 
                    string.Join(", ", insertResult.Errors), correlationId);
                
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    CorrelationId = correlationId,
                    Message = "Database insertion failed",
                    Errors = insertResult.Errors
                });
            }

            // Create success response
            var successResponse = new ApiResponse<CsvProcessingResponse>
            {
                Success = true,
                CorrelationId = correlationId,
                Message = "CSV processed successfully",
                Data = new CsvProcessingResponse
                {
                    TotalRowsProcessed = parseResult.TotalRowsProcessed,
                    ValidRowsCount = parseResult.ValidRowsCount,
                    InvalidRowsCount = parseResult.InvalidRowsCount,
                    RecordsInserted = insertResult.RecordsInserted,
                    ProcessingTimeMs = insertResult.ProcessingTime.TotalMilliseconds
                }
            };

            _logger.LogInformation("CSV processing completed successfully. Records inserted: {RecordsInserted}. CorrelationId: {CorrelationId}", 
                insertResult.RecordsInserted, correlationId);

            return Ok(successResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during CSV processing. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                CorrelationId = correlationId,
                Message = "An unexpected error occurred",
                Errors = new List<string> { ex.Message }
            });
        }
    }

    [HttpGet("health")]
    public async Task<IActionResult> HealthCheck()
    {
        _logger.LogInformation("Health check requested");

        try
        {
            var dbConnectionOk = await _dataService.TestConnectionAsync();
            
            var healthStatus = new ApiResponse<HealthCheckResponse>
            {
                Success = dbConnectionOk,
                Message = dbConnectionOk ? "Service is healthy" : "Service is unhealthy",
                Data = new HealthCheckResponse
                {
                    Status = dbConnectionOk ? "healthy" : "unhealthy",
                    Checks = new Dictionary<string, string>
                    {
                        { "database", dbConnectionOk ? "ok" : "failed" }
                    }
                }
            };

            var statusCode = dbConnectionOk ? 200 : 503;
            return StatusCode(statusCode, healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");
            return StatusCode(500, new ApiResponse<object>
            {
                Success = false,
                Message = "Health check failed",
                Errors = new List<string> { ex.Message }
            });
        }
    }
}
