using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ScoreboardCsvIngestor.Data;
using ScoreboardCsvIngestor.Models;
using System.Diagnostics;

namespace ScoreboardCsvIngestor.Services;

public class ScoreboardDataService : IScoreboardDataService
{
    private readonly ScoreboardDbContext _context;
    private readonly ILogger<ScoreboardDataService> _logger;

    public ScoreboardDataService(ScoreboardDbContext context, ILogger<ScoreboardDataService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<DataInsertResult> InsertRecordsAsync(List<CsvScoreboardRecord> records)
    {
        var result = new DataInsertResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting database insert operation for {RecordCount} records", records.Count);

            if (!records.Any())
            {
                result.IsSuccess = true;
                result.Errors.Add("No records to insert");
                return result;
            }

            // Convert CSV records to database entities
            var dbRecords = records.Select(r => r.ToScoreboardRecord()).ToList();

            // Use a transaction for data integrity
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // Add records in batches for better performance
                const int batchSize = 100;
                var batches = dbRecords.Chunk(batchSize);

                foreach (var batch in batches)
                {
                    await _context.ScoreboardRecords.AddRangeAsync(batch);
                    await _context.SaveChangesAsync();
                    result.RecordsInserted += batch.Length;
                    
                    _logger.LogDebug("Inserted batch of {BatchSize} records. Total inserted: {TotalInserted}", 
                        batch.Length, result.RecordsInserted);
                }

                await transaction.CommitAsync();
                result.IsSuccess = true;

                _logger.LogInformation("Successfully inserted {RecordsInserted} records into database", 
                    result.RecordsInserted);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error during database transaction. Rolling back changes");
                throw;
            }
        }
        catch (DbUpdateException dbEx)
        {
            _logger.LogError(dbEx, "Database update error during record insertion");
            result.IsSuccess = false;
            result.Errors.Add($"Database update error: {dbEx.InnerException?.Message ?? dbEx.Message}");
            result.RecordsFailed = records.Count - result.RecordsInserted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during record insertion");
            result.IsSuccess = false;
            result.Errors.Add($"Unexpected error: {ex.Message}");
            result.RecordsFailed = records.Count - result.RecordsInserted;
        }
        finally
        {
            stopwatch.Stop();
            result.ProcessingTime = stopwatch.Elapsed;
            _logger.LogInformation("Database insert operation completed in {ProcessingTime}ms. Success: {IsSuccess}", 
                result.ProcessingTime.TotalMilliseconds, result.IsSuccess);
        }

        return result;
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing database connection");
            await _context.Database.CanConnectAsync();
            _logger.LogInformation("Database connection test successful");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database connection test failed");
            return false;
        }
    }
}
