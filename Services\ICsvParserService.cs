using ScoreboardCsvIngestor.Models;

namespace ScoreboardCsvIngestor.Services;

public interface ICsvParserService
{
    Task<CsvParseResult> ParseCsvAsync(Stream csvStream);
    Task<CsvParseResult> ParseCsvAsync(string csvContent);
}

public class CsvParseResult
{
    public bool IsSuccess { get; set; }
    public List<CsvScoreboardRecord> Records { get; set; } = new();
    public List<string> Errors { get; set; } = new();
    public List<ValidationError> ValidationErrors { get; set; } = new();
    public int TotalRowsProcessed { get; set; }
    public int ValidRowsCount { get; set; }
    public int InvalidRowsCount { get; set; }
}

public class ValidationError
{
    public int RowNumber { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string? InvalidValue { get; set; }
}
